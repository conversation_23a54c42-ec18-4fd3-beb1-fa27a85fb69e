# IdeaBase GraphQL Schema
# 版本: 20250430144713

# 自定义标量类型
scalar Json
scalar Cursor
scalar DateTime

# 排序方向枚举，包含NULL值处理
enum SortDirection {
  ASC
  DESC
  ASC_NULLS_FIRST
  DESC_NULLS_FIRST
  ASC_NULLS_LAST
  DESC_NULLS_LAST
}

# 空值条件枚举
enum IsInput {
  NULL
  NOT_NULL
}

# ------------------ 分页相关类型 ------------------

# 页面信息（用于游标分页）
type PageInfo {
  hasNext: Boolean!  # 是否有下一页
  hasPrev: Boolean!  # 是否有上一页
  start: Cursor  # 当前页第一条记录的游标
  end: Cursor  # 当前页最后一条记录的游标
}

# 聚合分组选项
input GroupBy {
  fields: [String!]  # 分组字段
  having: Json  # 分组过滤条件
  limit: Int  # 分组结果限制
  sort: Json  # 分组结果排序
}

# 评论表
type Comment {
  # 子Comment列表
  children: [Comment]!
  # 评论内容
  content: String!
  createdAt: DateTime!
  id: ID!
  # 父Comment对象
  parent: Comment
  # 父评论ID
  parentId: Int
  # 关联的Post
  post: Post!
  # 评论文章
  postId: Int!
  # 关联的User
  user: User!
  # 评论者
  userId: Int!
}

# 文章表
type Post {
  # 关联的Comment列表
  comments: [Comment]!
  # 内容
  content: String
  createdAt: DateTime!
  id: ID!
  # 关联的Tag列表
  tags: [Tag]!
  # 标题
  title: String!
  # 关联的User
  user: User!
  # 作者ID
  userId: Int!
}

# 标签表
type Tag {
  createdAt: DateTime!
  id: ID!
  # 标签名称
  name: String!
  # 关联的Post列表
  posts: [Post]!
}

# 用户表
type User {
  # 关联的Comment列表
  comments: [Comment]!
  createdAt: DateTime!
  # 邮箱
  email: String!
  id: ID!
  # 用户名
  name: String!
  # 关联的Post列表
  posts: [Post]!
  updatedAt: DateTime
}

# ------------------ 连接和边类型（游标分页） ------------------

# Comment分页结果
type CommentPage {
  items: [Comment!]!  # 直接返回Comment对象数组
  pageInfo: PageInfo!
  total: Int!
}

# Post分页结果
type PostPage {
  items: [Post!]!  # 直接返回Post对象数组
  pageInfo: PageInfo!
  total: Int!
}

# Tag分页结果
type TagPage {
  items: [Tag!]!  # 直接返回Tag对象数组
  pageInfo: PageInfo!
  total: Int!
}

# User分页结果
type UserPage {
  items: [User!]!  # 直接返回User对象数组
  pageInfo: PageInfo!
  total: Int!
}

# ------------------ 聚合函数相关类型 ------------------

# 数值聚合结果
type NumberStats {
  sum: Float  # 总和
  avg: Float  # 平均值
  min: Float  # 最小值
  max: Float  # 最大值
  count: Int!  # 计数
  countDistinct: Int!  # 去重计数
}

# 日期聚合结果
type DateTimeStats {
  min: DateTime  # 最早时间
  max: DateTime  # 最晚时间
  count: Int!  # 计数
  countDistinct: Int!  # 去重计数
}

# 字符串聚合结果
type StringStats {
  min: String  # 最小值(按字典序)
  max: String  # 最大值(按字典序)
  count: Int!  # 计数
  countDistinct: Int!  # 去重计数
}

# Comment聚合
type CommentStats {
  count: Int!
  content: StringStats
  createdAt: DateTimeStats
  id: NumberStats
  parentId: NumberStats
  postId: NumberStats
  userId: NumberStats
  # 分组聚合
  groupBy: [CommentGroup!]
}

# Comment分组结果
type CommentGroup {
  key: Json!  # 分组键
  count: Int!  # 计数
  # 可以包含其他聚合字段
}

# Post聚合
type PostStats {
  count: Int!
  content: StringStats
  createdAt: DateTimeStats
  id: NumberStats
  title: StringStats
  userId: NumberStats
  # 分组聚合
  groupBy: [PostGroup!]
}

# Post分组结果
type PostGroup {
  key: Json!  # 分组键
  count: Int!  # 计数
  # 可以包含其他聚合字段
}

# Tag聚合
type TagStats {
  count: Int!
  createdAt: DateTimeStats
  id: NumberStats
  name: StringStats
  # 分组聚合
  groupBy: [TagGroup!]
}

# Tag分组结果
type TagGroup {
  key: Json!  # 分组键
  count: Int!  # 计数
  # 可以包含其他聚合字段
}

# User聚合
type UserStats {
  count: Int!
  createdAt: DateTimeStats
  email: StringStats
  id: NumberStats
  name: StringStats
  updatedAt: DateTimeStats
  # 分组聚合
  groupBy: [UserGroup!]
}

# User分组结果
type UserGroup {
  key: Json!  # 分组键
  count: Int!  # 计数
  # 可以包含其他聚合字段
}

# ------------------ 过滤器类型定义 ------------------

# Boolean过滤器
input BooleanFilter {
  eq: Boolean  # Equals value
  in: [Boolean!]  # Is in list of values
}

# DateTime过滤器
input DateTimeFilter {
  is: IsInput  # Is value null (true) or not null (false)
  eq: DateTime  # Equals value
  in: [DateTime!]  # Is in list of values
  hasKey: String  # Value is a JSON object with the specified key
  hasKeyAny: String  # Value is a JSON object with any of the specified keys
  hasKeyAll: String  # Value is a JSON object with all of the specified keys
  le: DateTime  # Is less than or equal to value
  ne: DateTime  # Does not equal value
}

# Float过滤器
input FloatFilter {
  is: IsInput  # Is value null (true) or not null (false)
  eq: Float  # Equals value
  in: [Float!]  # Is in list of values
  hasKey: String  # Value is a JSON object with the specified key
  hasKeyAny: String  # Value is a JSON object with any of the specified keys
  hasKeyAll: String  # Value is a JSON object with all of the specified keys
  le: Float  # Is less than or equal to value
  ne: Float  # Does not equal value
}

# ID过滤器
input IDFilter {
  eq: ID  # Equals value
  in: [ID!]  # Is in list of values
  hasKey: String  # Value is a JSON object with the specified key
  hasKeyAny: String  # Value is a JSON object with any of the specified keys
  hasKeyAll: String  # Value is a JSON object with all of the specified keys
  le: ID  # Is less than or equal to value
}

# Int过滤器
input IntFilter {
  is: IsInput  # Is value null (true) or not null (false)
  eq: Int  # Equals value
  in: [Int!]  # Is in list of values
  hasKey: String  # Value is a JSON object with the specified key
  hasKeyAny: String  # Value is a JSON object with any of the specified keys
  hasKeyAll: String  # Value is a JSON object with all of the specified keys
  le: Int  # Is less than or equal to value
  ne: Int  # Does not equal value
}

# Json过滤器
input JsonFilter {
  is: IsInput  # Is value null (true) or not null (false)
  eq: Json  # Equals value
  in: [Json!]  # Is in list of values
  hasKey: String  # Value is a JSON object with the specified key
  hasKeyAny: String  # Value is a JSON object with any of the specified keys
  hasKeyAll: String  # Value is a JSON object with all of the specified keys
}

# String过滤器
input StringFilter {
  is: IsInput  # Is value null (true) or not null (false)
  eq: String  # Equals value
  in: [String!]  # Is in list of values
  hasKey: String  # Value is a JSON object with the specified key
  hasKeyAny: String  # Value is a JSON object with any of the specified keys
  hasKeyAll: String  # Value is a JSON object with all of the specified keys
  le: String  # Is less than or equal to value
  ne: String  # Does not equal value
  like: String  # Value matching pattern where '%' represents zero or more characters and '_' represents a single character. Eg. '_r%' finds values having 'r' in second position
  iLike: String  # Value matching (case-insensitive) pattern where '%' represents zero or more characters and '_' represents a single character. Eg. '_r%' finds values not having 'r' in second position
  regex: String  # Value matching regular pattern
  iRegex: String  # Value matching (case-insensitive) regex pattern
}

# Comment查询条件
input CommentFilter {
  content: StringFilter
  createdAt: DateTimeFilter
  id: IDFilter
  parentId: IntFilter
  postId: IntFilter
  userId: IntFilter
  and: [CommentFilter!]
  or: [CommentFilter!]
  not: CommentFilter
}

# Post查询条件
input PostFilter {
  content: StringFilter
  createdAt: DateTimeFilter
  id: IDFilter
  title: StringFilter
  userId: IntFilter
  and: [PostFilter!]
  or: [PostFilter!]
  not: PostFilter
}

# Tag查询条件
input TagFilter {
  createdAt: DateTimeFilter
  id: IDFilter
  name: StringFilter
  and: [TagFilter!]
  or: [TagFilter!]
  not: TagFilter
}

# User查询条件
input UserFilter {
  createdAt: DateTimeFilter
  email: StringFilter
  id: IDFilter
  name: StringFilter
  updatedAt: DateTimeFilter
  and: [UserFilter!]
  or: [UserFilter!]
  not: UserFilter
}

# Comment排序
input CommentSort {
  children: SortDirection
  content: SortDirection
  createdAt: SortDirection
  id: SortDirection
  parent: SortDirection
  parentId: SortDirection
  post: SortDirection
  postId: SortDirection
  user: SortDirection
  userId: SortDirection
}

# Post排序
input PostSort {
  comments: SortDirection
  content: SortDirection
  createdAt: SortDirection
  id: SortDirection
  tags: SortDirection
  title: SortDirection
  user: SortDirection
  userId: SortDirection
}

# Tag排序
input TagSort {
  createdAt: SortDirection
  id: SortDirection
  name: SortDirection
  posts: SortDirection
}

# User排序
input UserSort {
  comments: SortDirection
  createdAt: SortDirection
  email: SortDirection
  id: SortDirection
  name: SortDirection
  posts: SortDirection
  updatedAt: SortDirection
}

# Comment创建输入
input CommentCreateInput {
  content: String!
  createdAt: DateTime!
  id: ID!
  parentId: Int
  postId: Int!
  userId: Int!
}

# Comment更新输入
input CommentUpdateInput {
  content: String
  parentId: Int
  postId: Int
  userId: Int
  # 关系操作
  relation: RelationInput
}

# Post创建输入
input PostCreateInput {
  content: String
  createdAt: DateTime!
  id: ID!
  title: String!
  userId: Int!
}

# Post更新输入
input PostUpdateInput {
  content: String
  title: String
  userId: Int
  # 关系操作
  relation: RelationInput
}

# Tag创建输入
input TagCreateInput {
  createdAt: DateTime!
  id: ID!
  name: String!
}

# Tag更新输入
input TagUpdateInput {
  name: String
  # 关系操作
  relation: RelationInput
}

# User创建输入
input UserCreateInput {
  createdAt: DateTime!
  email: String!
  id: ID!
  name: String!
  updatedAt: DateTime
}

# User更新输入
input UserUpdateInput {
  email: String
  name: String
  # 关系操作
  relation: RelationInput
}

# 关联操作
input RelationInput {
  id: ID!
  connect: [ID!]
  disconnect: [ID!]
}

# ------------------ 查询和变更 ------------------

# 查询根类型
type Query {
  # 单个Comment查询
  comment(id: ID!): Comment

  # Comment列表查询
  comments(
    filter: CommentFilter
    sort: [CommentSort!]
    limit: Int
    offset: Int
    first: Int
    last: Int
    after: Cursor
    before: Cursor
  ): CommentPage!

  # Comment统计查询
  commentStats(filter: CommentFilter, groupBy: GroupBy): CommentStats!
  # 单个Post查询
  post(id: ID!): Post

  # Post列表查询
  posts(
    filter: PostFilter
    sort: [PostSort!]
    limit: Int
    offset: Int
    first: Int
    last: Int
    after: Cursor
    before: Cursor
  ): PostPage!

  # Post统计查询
  postStats(filter: PostFilter, groupBy: GroupBy): PostStats!
  # 单个Tag查询
  tag(id: ID!): Tag

  # Tag列表查询
  tags(
    filter: TagFilter
    sort: [TagSort!]
    limit: Int
    offset: Int
    first: Int
    last: Int
    after: Cursor
    before: Cursor
  ): TagPage!

  # Tag统计查询
  tagStats(filter: TagFilter, groupBy: GroupBy): TagStats!
  # 单个User查询
  user(id: ID!): User

  # User列表查询
  users(
    filter: UserFilter
    sort: [UserSort!]
    limit: Int
    offset: Int
    first: Int
    last: Int
    after: Cursor
    before: Cursor
  ): UserPage!

  # User统计查询
  userStats(filter: UserFilter, groupBy: GroupBy): UserStats!
}

# 突变根类型
type Mutation {
  # Comment创建
  createComment(data: CommentCreateInput!): Comment!

  # Comment更新
  updateComment(id: ID!, data: CommentUpdateInput!): Comment!

  # Comment删除
  deleteComment(id: ID!): Boolean!

  # Comment批量删除
  batchDeleteComment(filter: CommentFilter!): Int!
  # Post创建
  createPost(data: PostCreateInput!): Post!

  # Post更新
  updatePost(id: ID!, data: PostUpdateInput!): Post!

  # Post删除
  deletePost(id: ID!): Boolean!

  # Post批量删除
  batchDeletePost(filter: PostFilter!): Int!
  # Tag创建
  createTag(data: TagCreateInput!): Tag!

  # Tag更新
  updateTag(id: ID!, data: TagUpdateInput!): Tag!

  # Tag删除
  deleteTag(id: ID!): Boolean!

  # Tag批量删除
  batchDeleteTag(filter: TagFilter!): Int!
  # User创建
  createUser(data: UserCreateInput!): User!

  # User更新
  updateUser(id: ID!, data: UserUpdateInput!): User!

  # User删除
  deleteUser(id: ID!): Boolean!

  # User批量删除
  batchDeleteUser(filter: UserFilter!): Int!
}
